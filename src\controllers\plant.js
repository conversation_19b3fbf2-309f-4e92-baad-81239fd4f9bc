import Plant from '../models/Plant'
import PlantGrowthRecord from '../models/PlantGrowthRecord'

export default class PlantController {
  /**
   * @swagger
   * /plant/list:
   *   get:
   *     tags:
   *       - 多肉模块
   *     summary: 获取多肉列表
   *     description: 获取当前用户的多肉列表
   *     security:
   *       - Bearer: []
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 data:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/Plant'
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async getPlants(ctx) {
    const userId = ctx.state.user.id

    try {
      const plants = await Plant.findAll({
        where: { user_id: userId },
        order: [['created_at', 'ASC']]
      })

      ctx.body = {
        code: 200,
        data: plants
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取多肉列表失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /plant/{id}:
   *   get:
   *     tags:
   *       - 多肉模块
   *     summary: 获取多肉详情
   *     description: 获取指定多肉的详细信息和成长记录
   *     security:
   *       - Bearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 多肉ID
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 data:
   *                   type: object
   *                   properties:
   *                     plant:
   *                       $ref: '#/components/schemas/Plant'
   *                     growth_records:
   *                       type: array
   *                       items:
   *                         $ref: '#/components/schemas/PlantGrowthRecord'
   *       404:
   *         description: 多肉不存在
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async getPlantDetail(ctx) {
    const userId = ctx.state.user.id
    const { id } = ctx.params

    try {
      const plant = await Plant.findOne({
        where: { id, user_id: userId }
      })

      if (!plant) {
        ctx.body = {
          code: 404,
          message: '多肉不存在'
        }
        return
      }

      // 获取成长记录
      const growthRecords = await PlantGrowthRecord.findAll({
        where: { plant_id: id },
        order: [['created_at', 'DESC']],
        limit: 10
      })

      ctx.body = {
        code: 200,
        data: {
          plant,
          growth_records: growthRecords
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取多肉详情失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /plant/{id}/energy:
   *   post:
   *     tags:
   *       - 多肉模块
   *     summary: 为多肉增加能量
   *     description: 为指定多肉增加能量值，可能触发升级
   *     security:
   *       - Bearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 多肉ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - energy_value
   *             properties:
   *               energy_value:
   *                 type: integer
   *                 minimum: 1
   *                 description: 增加的能量值
   *                 example: 10
   *               reason:
   *                 type: string
   *                 description: 增加能量的原因
   *                 example: "完成冥想"
   *     responses:
   *       200:
   *         description: 能量添加成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "能量添加成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     plant:
   *                       $ref: '#/components/schemas/Plant'
   *                     level_up:
   *                       type: boolean
   *                       description: 是否升级了
   *       400:
   *         description: 请求参数错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       404:
   *         description: 多肉不存在
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async addEnergy(ctx) {
    const userId = ctx.state.user.id
    const { id } = ctx.params
    const { energy_value, reason = '手动添加能量' } = ctx.request.body

    if (!energy_value || energy_value <= 0) {
      ctx.body = {
        code: 400,
        message: '能量值必须大于0'
      }
      return
    }

    try {
      const plant = await Plant.findOne({
        where: { id, user_id: userId }
      })

      if (!plant) {
        ctx.body = {
          code: 404,
          message: '多肉不存在'
        }
        return
      }

      // 更新多肉能量值
      const newEnergyValue = plant.energy_value + parseInt(energy_value)
      let newLevel = plant.level

      // 简单的升级逻辑：每100能量升1级
      if (newEnergyValue >= plant.level * 100) {
        newLevel = Math.floor(newEnergyValue / 100) + 1
      }

      await plant.update({
        energy_value: newEnergyValue,
        level: newLevel,
        updated_at: new Date()
      })

      // 记录成长记录
      await PlantGrowthRecord.create({
        plant_id: id,
        change_value: parseInt(energy_value),
        reason
      })

      ctx.body = {
        code: 200,
        message: '能量添加成功',
        data: {
          plant,
          level_up: newLevel > plant.level
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '添加能量失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /plant/create:
   *   post:
   *     tags:
   *       - 多肉模块
   *     summary: 创建新多肉
   *     description: 为当前用户创建一个新的多肉
   *     security:
   *       - Bearer: []
   *     requestBody:
   *       required: false
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               species:
   *                 type: string
   *                 description: 多肉品种
   *                 default: "succulent"
   *                 example: "succulent"
   *     responses:
   *       200:
   *         description: 多肉创建成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "多肉创建成功"
   *                 data:
   *                   $ref: '#/components/schemas/Plant'
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async createPlant(ctx) {
    const userId = ctx.state.user.id
    const { species = 'succulent' } = ctx.request.body

    try {
      const plant = await Plant.create({
        user_id: userId,
        species,
        energy_value: 0,
        level: 1
      })

      // 记录创建记录
      await PlantGrowthRecord.create({
        plant_id: plant.id,
        change_value: 0,
        reason: '创建新多肉'
      })

      ctx.body = {
        code: 200,
        message: '多肉创建成功',
        data: plant
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '创建多肉失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /plant/{id}/records:
   *   get:
   *     tags:
   *       - 多肉模块
   *     summary: 获取多肉成长记录
   *     description: 获取指定多肉的成长记录列表
   *     security:
   *       - Bearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 多肉ID
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 20
   *         description: 每页数量
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/PaginatedResponse'
   *       404:
   *         description: 多肉不存在
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async getGrowthRecords(ctx) {
    const userId = ctx.state.user.id
    const { id } = ctx.params
    const { page = 1, limit = 20 } = ctx.query

    try {
      // 验证多肉是否属于当前用户
      const plant = await Plant.findOne({
        where: { id, user_id: userId }
      })

      if (!plant) {
        ctx.body = {
          code: 404,
          message: '多肉不存在'
        }
        return
      }

      const offset = (page - 1) * limit
      const records = await PlantGrowthRecord.findAndCountAll({
        where: { plant_id: id },
        limit: parseInt(limit),
        offset: offset,
        order: [['created_at', 'DESC']]
      })

      ctx.body = {
        code: 200,
        data: {
          total: records.count,
          page: parseInt(page),
          limit: parseInt(limit),
          items: records.rows
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取成长记录失败',
        error: error.message
      }
    }
  }
}