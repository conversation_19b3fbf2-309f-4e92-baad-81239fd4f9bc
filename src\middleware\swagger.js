import swaggerJSDoc from 'swagger-jsdoc'
import { koaSwagger } from 'koa2-swagger-ui'
import { Swagger } from '../config'
import path from 'path'

// Swagger 配置选项
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: Swagger.title,
      version: Swagger.version,
      description: Swagger.description,
    },
    servers: [
      {
        url: `http://${Swagger.host}${Swagger.basePath}`,
        description: '开发环境'
      }
    ],
    components: {
      securitySchemes: {
        Bearer: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token, 格式: Bearer <token>'
        }
      },
      schemas: {
        // 通用响应结构
        ApiResponse: {
          type: 'object',
          properties: {
            code: {
              type: 'integer',
              description: '状态码'
            },
            message: {
              type: 'string',
              description: '响应消息'
            },
            data: {
              type: 'object',
              description: '响应数据'
            }
          }
        },
        // 分页响应结构
        PaginatedResponse: {
          type: 'object',
          properties: {
            code: {
              type: 'integer',
              description: '状态码'
            },
            data: {
              type: 'object',
              properties: {
                total: {
                  type: 'integer',
                  description: '总条数'
                },
                pageNum: {
                  type: 'integer',
                  description: '当前页码'
                },
                pageSize: {
                  type: 'integer',
                  description: '页容量'
                },
                pages: {
                  type: 'integer',
                  description: '总页数'
                },
                items: {
                  type: 'array',
                  items: {
                    type: 'object'
                  },
                  description: '数据列表'
                }
              }
            }
          }
        },
        // 用户模型
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              description: '用户ID'
            },
            openid: {
              type: 'string',
              description: '微信openid'
            },
            unionid: {
              type: 'string',
              description: '微信unionid'
            },
            nickname: {
              type: 'string',
              description: '用户昵称'
            },
            avatar_url: {
              type: 'string',
              description: '头像URL'
            },
            meditation_level: {
              type: 'integer',
              description: '冥想等级'
            },
            streak_days: {
              type: 'integer',
              description: '连续天数'
            }
          }
        },
        // 冥想内容模型
        MeditationContent: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              description: '内容ID'
            },
            title: {
              type: 'string',
              description: '标题'
            },
            description: {
              type: 'string',
              description: '描述'
            },
            type: {
              type: 'string',
              enum: ['audio', 'video', 'text'],
              description: '内容类型'
            },
            sub_type: {
              type: 'string',
              description: '子类型'
            },
            duration: {
              type: 'integer',
              description: '时长(秒)'
            },
            cover_url: {
              type: 'string',
              description: '封面图片URL'
            },
            content_url: {
              type: 'string',
              description: '内容URL'
            },
            favorite_count: {
              type: 'integer',
              description: '收藏数'
            }
          }
        },
        // 多肉模型
        Plant: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              description: '多肉ID'
            },
            user_id: {
              type: 'integer',
              description: '用户ID'
            },
            species: {
              type: 'string',
              description: '品种'
            },
            energy_value: {
              type: 'integer',
              description: '能量值'
            },
            level: {
              type: 'integer',
              description: '等级'
            },
            created_at: {
              type: 'string',
              format: 'date-time',
              description: '创建时间'
            },
            updated_at: {
              type: 'string',
              format: 'date-time',
              description: '更新时间'
            }
          }
        },

        // 多肉成长记录模型
        PlantGrowthRecord: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              description: '记录ID'
            },
            plant_id: {
              type: 'integer',
              description: '多肉ID'
            },
            change_value: {
              type: 'integer',
              description: '变化值'
            },
            reason: {
              type: 'string',
              description: '变化原因'
            },
            created_at: {
              type: 'string',
              format: 'date-time',
              description: '创建时间'
            }
          }
        }
      }
    }
  },
  apis: [
    path.join(__dirname, '../controllers/*.js'),
    path.join(__dirname, '../routes/*.js')
  ]
}

// 生成 Swagger 规范
const swaggerSpec = swaggerJSDoc(swaggerOptions)

// Swagger UI 中间件
export const swaggerUI = koaSwagger({
  routePrefix: '/swagger',
  swaggerOptions: {
    spec: swaggerSpec,
  },
})

// 导出 Swagger 规范 JSON
export const swaggerJSON = (ctx) => {
  ctx.set('Content-Type', 'application/json')
  ctx.body = swaggerSpec
}

export default swaggerSpec
